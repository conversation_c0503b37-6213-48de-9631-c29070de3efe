<template>
  <div class="flex items-center gap-2">
    <!-- 语言选择器 -->
    <DropdownMenu>
      <DropdownMenuTrigger as-child>
        <Button variant="ghost" size="sm" class="h-8 px-2">
          <span class="text-sm">{{ currentLocaleInfo.flag }}</span>
          <span class="ml-1 text-xs">{{ currentLocaleInfo.name }}</span>
          <MattIcon name="ChevronDown" class="ml-1 h-3 w-3" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" class="w-40">
        <DropdownMenuLabel class="text-xs">
          {{ $t('components.language_selector.title') }}
        </DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuItem
          v-for="localeOption in SUPPORTED_LOCALES"
          :key="localeOption.code"
          @click="switchLanguage(localeOption.code)"
          :class="{ 'bg-accent': localeOption.code === currentLocale }"
          class="text-sm"
        >
          <span class="mr-2">{{ localeOption.flag }}</span>
          <span>{{ localeOption.name }}</span>
          <MattIcon
            v-if="localeOption.code === currentLocale"
            name="Check"
            class="ml-auto h-3 w-3"
          />
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>

    <!-- 主题切换器 -->
    <DropdownMenu>
      <DropdownMenuTrigger as-child>
        <Button variant="ghost" size="sm" class="h-8 px-2">
          <MattIcon :name="currentThemeIcon" class="h-4 w-4" />
          <MattIcon name="ChevronDown" class="ml-1 h-3 w-3" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" class="w-36">
        <DropdownMenuLabel class="text-xs">
          {{ $t('components.theme_selector.title') }}
        </DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuItem
          v-for="themeOption in themeOptions"
          :key="themeOption.value"
          @click="switchTheme(themeOption.value)"
          :class="{ 'bg-accent': themeOption.value === selectedTheme }"
          class="text-sm"
        >
          <MattIcon :name="themeOption.icon" class="mr-2 h-4 w-4" />
          <span>{{ themeOption.label }}</span>
          <MattIcon
            v-if="themeOption.value === selectedTheme"
            name="Check"
            class="ml-auto h-3 w-3"
          />
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useI18n, SUPPORTED_LOCALES, type SupportedLocale } from '@mattverse/i18n'
import { useSettingsStore, type ThemeMode } from '@/store'
import { setDocumentLang } from '@mattverse/i18n'
import { logger } from '@mattverse/shared'

const { t, locale } = useI18n()
const settingsStore = useSettingsStore()

// 语言相关
const currentLocale = computed(() => locale.value as SupportedLocale)

const currentLocaleInfo = computed(() => {
  return SUPPORTED_LOCALES.find(l => l.code === currentLocale.value) || SUPPORTED_LOCALES[0]
})

const switchLanguage = (newLocale: SupportedLocale) => {
  // 更新设置状态
  settingsStore.setLanguage(newLocale)

  // 更新i18n实例的语言
  locale.value = newLocale

  // 设置文档语言
  setDocumentLang(newLocale)

  logger.info(`语言已切换为 ${newLocale}`)
}

// 主题相关
const selectedTheme = computed(() => settingsStore.theme)
const currentTheme = computed(() => settingsStore.currentTheme)

const currentThemeIcon = computed(() => {
  const option = themeOptions.value.find(opt => opt.value === currentTheme.value)
  return option?.icon || 'Monitor'
})

const themeOptions = computed(() => [
  {
    value: 'light' as ThemeMode,
    label: t('settings.theme_options.light'),
    icon: 'Sun',
  },
  {
    value: 'dark' as ThemeMode,
    label: t('settings.theme_options.dark'),
    icon: 'Moon',
  },
  {
    value: 'system' as ThemeMode,
    label: t('settings.theme_options.system'),
    icon: 'Monitor',
  },
])

const switchTheme = (themeValue: ThemeMode) => {
  settingsStore.setTheme(themeValue)
}
</script>
